package com.hky.hr.organization.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Feign客户端配置
 *
 * 配置平台服务Feign客户端：
 * - 审计服务 (8004)
 * - 工作流服务 (8006)
 * - 通知服务 (8007)
 * - 字典服务 (8008)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Configuration
public class FeignConfig {

    /**
     * Feign日志级别配置
     *
     * @return Logger.Level
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;  // 改为 FULL 以便详细调试
    }

    /**
     * Feign请求超时配置
     *
     * @return Request.Options
     */
    @Bean
    public Request.Options requestOptions() {
        // 连接超时时间：5秒
        // 读取超时时间：10秒
        return new Request.Options(
                5000, TimeUnit.MILLISECONDS,
                10000, TimeUnit.MILLISECONDS,
                true
        );
    }

    /**
     * Feign重试策略配置
     *
     * @return Retryer
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔：100ms
        // 最大重试间隔：1000ms
        // 最大重试次数：3次
        return new Retryer.Default(100, 1000, 3);
    }
}
