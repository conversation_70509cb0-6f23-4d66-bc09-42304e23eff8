package com.hky.hr.organization.entity;

import com.hky.hr.organization.enums.PositionType;
import com.hky.hr.organization.enums.Status;
import com.hzwangda.edu.common.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 岗位实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "org_position", indexes = {
    @Index(name = "idx_position_code", columnList = "positionCode", unique = true),
    @Index(name = "idx_organization_id", columnList = "organizationId"),
    @Index(name = "idx_position_type", columnList = "positionType"),
    @Index(name = "idx_pos_status", columnList = "status")
})
public class Position extends BaseEntity {

    /**
     * 岗位编码
     */
    @NotBlank(message = "岗位编码不能为空")
    @Size(max = 64, message = "岗位编码长度不能超过64个字符")
    @Column(name = "position_code", length = 64, nullable = false, unique = true)
    private String positionCode;

    /**
     * 岗位名称
     */
    @NotBlank(message = "岗位名称不能为空")
    @Size(max = 128, message = "岗位名称长度不能超过128个字符")
    @Column(name = "position_name", length = 128, nullable = false)
    private String positionName;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织不能为空")
    @Column(name = "organization_id", nullable = false)
    private Long organizationId;

    /**
     * 岗位类型
     */
    @NotNull(message = "岗位类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "position_type", length = 32, nullable = false)
    private PositionType positionType;

    /**
     * 岗位级别
     */
    @Size(max = 32, message = "岗位级别长度不能超过32个字符")
    @Column(name = "position_level", length = 32)
    private String positionLevel;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 岗位职责
     */
    @Column(name = "responsibilities", columnDefinition = "TEXT")
    private String responsibilities;

    /**
     * 任职要求
     */
    @Column(name = "requirements", columnDefinition = "TEXT")
    private String requirements;

    /**
     * 最低薪资
     */
    @DecimalMin(value = "0.00", message = "最低薪资不能小于0")
    @Column(name = "min_salary", precision = 10, scale = 2)
    private BigDecimal minSalary;

    /**
     * 最高薪资
     */
    @DecimalMin(value = "0.00", message = "最高薪资不能小于0")
    @Column(name = "max_salary", precision = 10, scale = 2)
    private BigDecimal maxSalary;

    /**
     * 编制人数
     */
    @Min(value = 0, message = "编制人数不能小于0")
    @Column(name = "headcount")
    private Integer headcount;

    /**
     * 当前人数
     */
    @Min(value = 0, message = "当前人数不能小于0")
    @Column(name = "current_count")
    private Integer currentCount = 0;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private Status status = Status.ACTIVE;

    /**
     * 所属组织（关联关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", insertable = false, updatable = false)
    private Organization organization;

    /**
     * 计算空缺人数
     */
    public Integer getVacantCount() {
        if (headcount == null || currentCount == null) {
            return 0;
        }
        return Math.max(0, headcount - currentCount);
    }

    /**
     * 判断是否有空缺
     */
    public boolean hasVacancy() {
        return getVacantCount() > 0;
    }

    /**
     * 判断是否超编
     */
    public boolean isOverstaffed() {
        if (headcount == null || currentCount == null) {
            return false;
        }
        return currentCount > headcount;
    }
}
