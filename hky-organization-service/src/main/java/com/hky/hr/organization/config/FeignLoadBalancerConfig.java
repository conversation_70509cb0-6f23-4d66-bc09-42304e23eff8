package com.hky.hr.organization.config;

import feign.Client;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Feign LoadBalancer配置类
 * 
 * 解决Feign客户端使用lb://协议时的"unknown protocol: lb"错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class FeignLoadBalancerConfig {

    /**
     * 配置支持LoadBalancer的RestTemplate
     */
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 配置Feign LoadBalancer客户端
     */
    @Bean
    public Client feignClient(LoadBalancerClientFactory loadBalancerClientFactory) {
        return new FeignBlockingLoadBalancerClient(new Client.Default(null, null), loadBalancerClientFactory);
    }
}
