package com.hky.hr.organization.service.impl;

import com.hky.hr.organization.dto.OrganizationDTO;
import com.hky.hr.organization.dto.OrganizationQueryDTO;
import com.hky.hr.organization.entity.Organization;
import com.hky.hr.organization.repository.OrganizationRepository;
import com.hky.hr.organization.repository.PositionRepository;
import com.hky.hr.organization.service.OrganizationService;
import com.hzwangda.edu.common.client.AuditLogClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组织管理服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepository organizationRepository;
    private final PositionRepository positionRepository;
    private final AuditLogClient auditLogClient;

    @Override
    @Transactional
    public OrganizationDTO createOrganization(OrganizationDTO organizationDTO) {
        log.info("创建组织机构: {}", organizationDTO.getOrgName());

        // 检查组织编码是否已存在
        if (organizationRepository.existsByOrgCodeAndDeletedFalse(organizationDTO.getOrgCode())) {
            throw new IllegalArgumentException("组织编码已存在: " + organizationDTO.getOrgCode());
        }

        Organization organization = organizationDTO.toEntity();

        // 设置组织层级和父级路径
        if (organization.getParentId() != null) {
            Organization parent = organizationRepository.findById(organization.getParentId())
                .orElseThrow(() -> new IllegalArgumentException("父级组织不存在"));

            organization.setOrgLevel(parent.getOrgLevel() + 1);

            String parentPath = StringUtils.hasText(parent.getParentPath())
                ? parent.getParentPath() + "/" + parent.getId()
                : "/" + parent.getId();
            organization.setParentPath(parentPath);
        } else {
            organization.setOrgLevel(1);
            organization.setParentPath("/");
        }

        // 设置排序顺序
        if (organization.getSortOrder() == null) {
            Integer maxSortOrder = organizationRepository.findMaxSortOrderByParentId(organization.getParentId());
            organization.setSortOrder(maxSortOrder + 1);
        }

        Organization savedOrganization = organizationRepository.save(organization);
        log.info("组织机构创建成功: ID={}, 名称={}", savedOrganization.getId(), savedOrganization.getOrgName());

        // 记录审计日志
        AuditLogClient.AuditLogRequest auditLogRequest = new AuditLogClient.AuditLogRequest(
                "CREATE",
                "组织机构创建",
                "组织机构创建",
                "组织机构创建",
                savedOrganization.getId().toString(),
                savedOrganization.getOrgName(),
                "1",
                "admin",
                "ip"
        );
        auditLogClient.recordAuditLog(auditLogRequest);

        return OrganizationDTO.fromEntity(savedOrganization);
    }

    @Override
    @Transactional
    public OrganizationDTO updateOrganization(Long id, OrganizationDTO organizationDTO) {
        log.info("更新组织机构: ID={}, 名称={}", id, organizationDTO.getOrgName());

        Organization existingOrganization = organizationRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + id));

        if (existingOrganization.getDeleted()) {
            throw new IllegalArgumentException("组织已被删除，无法更新: " + id);
        }

        // 检查组织编码是否已存在（排除当前组织）
        if (organizationRepository.existsByOrgCodeAndDeletedFalseAndIdNot(organizationDTO.getOrgCode(), id)) {
            throw new IllegalArgumentException("组织编码已存在: " + organizationDTO.getOrgCode());
        }

        // 更新组织信息
        existingOrganization.setOrgCode(organizationDTO.getOrgCode());
        existingOrganization.setOrgName(organizationDTO.getOrgName());
        existingOrganization.setOrgShortName(organizationDTO.getOrgShortName());
        existingOrganization.setOrgType(organizationDTO.getOrgType());
        existingOrganization.setDescription(organizationDTO.getDescription());
        existingOrganization.setLeader(organizationDTO.getLeader());
        existingOrganization.setPhone(organizationDTO.getPhone());
        existingOrganization.setEmail(organizationDTO.getEmail());
        existingOrganization.setAddress(organizationDTO.getAddress());
        existingOrganization.setStatus(organizationDTO.getStatus());
        existingOrganization.setEffectiveDate(organizationDTO.getEffectiveDate());
        existingOrganization.setWithdrawDate(organizationDTO.getWithdrawDate());
        existingOrganization.setApprovalDocNumber(organizationDTO.getApprovalDocNumber());
        existingOrganization.setApprovalDate(organizationDTO.getApprovalDate());
        existingOrganization.setEstablishmentDate(organizationDTO.getEstablishmentDate());
        existingOrganization.setIsVirtual(organizationDTO.getIsVirtual());
        existingOrganization.setIsTemporary(organizationDTO.getIsTemporary());
        existingOrganization.setRemark(organizationDTO.getRemark());

        Organization savedOrganization = organizationRepository.save(existingOrganization);
        log.info("组织机构更新成功: ID={}, 名称={}", savedOrganization.getId(), savedOrganization.getOrgName());

        return OrganizationDTO.fromEntity(savedOrganization);
    }

    @Override
    @Transactional
    public void deleteOrganization(Long id) {
        log.info("删除组织机构: ID={}", id);

        Organization organization = organizationRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + id));

        if (organization.getDeleted()) {
            throw new IllegalArgumentException("组织已被删除: " + id);
        }

        // 检查是否有子组织
        long childrenCount = organizationRepository.countByParentIdAndDeletedFalse(id);
        if (childrenCount > 0) {
            throw new IllegalArgumentException("存在子组织，无法删除");
        }

        // 检查是否有岗位
        long positionCount = positionRepository.countByOrganizationIdAndDeletedFalse(id);
        if (positionCount > 0) {
            throw new IllegalArgumentException("存在岗位，无法删除");
        }

        // 软删除
        organization.setDeleted(true);
        organizationRepository.save(organization);

        log.info("组织机构删除成功: ID={}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public OrganizationDTO getOrganizationById(Long id) {
        Organization organization = organizationRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + id));

        if (organization.getDeleted()) {
            throw new IllegalArgumentException("组织已被删除: " + id);
        }

        OrganizationDTO dto = OrganizationDTO.fromEntity(organization);

        // 设置岗位数量
        long positionCount = positionRepository.countByOrganizationIdAndDeletedFalse(id);
        dto.setPositionCount(positionCount);

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public OrganizationDTO getOrganizationByCode(String orgCode) {
        Organization organization = organizationRepository.findByOrgCodeAndDeletedFalse(orgCode)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + orgCode));

        OrganizationDTO dto = OrganizationDTO.fromEntity(organization);

        // 设置岗位数量
        long positionCount = positionRepository.countByOrganizationIdAndDeletedFalse(organization.getId());
        dto.setPositionCount(positionCount);

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrganizationDTO> queryOrganizations(OrganizationQueryDTO queryDTO) {
        // 构建排序
        Sort sort = Sort.by(
            "desc".equalsIgnoreCase(queryDTO.getSortDirection())
                ? Sort.Direction.DESC
                : Sort.Direction.ASC,
            queryDTO.getSortBy()
        );

        Pageable pageable = PageRequest.of(queryDTO.getPage(), queryDTO.getSize(), sort);

        Page<Organization> organizationPage = organizationRepository.findByConditions(
            queryDTO.getOrgName(),
            queryDTO.getOrgType(),
            queryDTO.getStatus(),
            queryDTO.getParentId(),
            pageable
        );

        return organizationPage.map(OrganizationDTO::fromEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrganizationDTO> getOrganizationTree() {
        List<Organization> rootOrganizations = organizationRepository.findByParentIdIsNullAndDeletedFalseOrderBySortOrderAsc();
        return rootOrganizations.stream()
            .map(this::buildOrganizationTree)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrganizationDTO> getChildrenOrganizations(Long parentId) {
        List<Organization> children = organizationRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(parentId);
        return children.stream()
            .map(OrganizationDTO::fromEntity)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrganizationDTO> getAllChildrenOrganizations(Long parentId) {
        Organization parent = organizationRepository.findById(parentId)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + parentId));

        String parentPath = StringUtils.hasText(parent.getParentPath())
            ? parent.getParentPath() + "/" + parent.getId()
            : "/" + parent.getId();

        List<Organization> allChildren = organizationRepository.findAllChildrenByParentPath(parentPath);
        return allChildren.stream()
            .map(OrganizationDTO::fromEntity)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void moveOrganization(Long organizationId, Long newParentId) {
        log.info("移动组织: ID={}, 新父级ID={}", organizationId, newParentId);

        Organization organization = organizationRepository.findById(organizationId)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + organizationId));

        if (organization.getDeleted()) {
            throw new IllegalArgumentException("组织已被删除: " + organizationId);
        }

        // 检查新父级组织
        if (newParentId != null) {
            Organization newParent = organizationRepository.findById(newParentId)
                .orElseThrow(() -> new IllegalArgumentException("新父级组织不存在: " + newParentId));

            if (newParent.getDeleted()) {
                throw new IllegalArgumentException("新父级组织已被删除: " + newParentId);
            }

            // 检查是否会形成循环引用
            if (isCircularReference(organizationId, newParentId)) {
                throw new IllegalArgumentException("不能将组织移动到其子组织下");
            }

            organization.setParentId(newParentId);
            organization.setOrgLevel(newParent.getOrgLevel() + 1);

            String parentPath = StringUtils.hasText(newParent.getParentPath())
                ? newParent.getParentPath() + "/" + newParent.getId()
                : "/" + newParent.getId();
            organization.setParentPath(parentPath);
        } else {
            organization.setParentId(null);
            organization.setOrgLevel(1);
            organization.setParentPath("/");
        }

        organizationRepository.save(organization);

        // 更新所有子组织的层级和路径
        updateChildrenLevelAndPath(organization);

        log.info("组织移动成功: ID={}", organizationId);
    }

    @Override
    @Transactional
    public void updateSortOrder(Long organizationId, Integer sortOrder) {
        log.info("更新组织排序: ID={}, 排序={}", organizationId, sortOrder);

        Organization organization = organizationRepository.findById(organizationId)
            .orElseThrow(() -> new IllegalArgumentException("组织不存在: " + organizationId));

        if (organization.getDeleted()) {
            throw new IllegalArgumentException("组织已被删除: " + organizationId);
        }

        organization.setSortOrder(sortOrder);
        organizationRepository.save(organization);

        log.info("组织排序更新成功: ID={}", organizationId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByOrgCode(String orgCode, Long excludeId) {
        if (excludeId != null) {
            return organizationRepository.existsByOrgCodeAndDeletedFalseAndIdNot(orgCode, excludeId);
        } else {
            return organizationRepository.existsByOrgCodeAndDeletedFalse(orgCode);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Object getOrganizationStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总组织数量
        long totalCount = organizationRepository.count();
        statistics.put("totalCount", totalCount);

        // 各类型组织数量
        // 这里可以添加更多统计逻辑

        return statistics;
    }

    /**
     * 递归构建组织树
     */
    private OrganizationDTO buildOrganizationTree(Organization organization) {
        OrganizationDTO dto = OrganizationDTO.fromEntity(organization);

        List<Organization> children = organizationRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(organization.getId());
        List<OrganizationDTO> childrenDTOs = children.stream()
            .map(this::buildOrganizationTree)
            .collect(Collectors.toList());

        dto.setChildren(childrenDTOs);

        // 设置岗位数量
        long positionCount = positionRepository.countByOrganizationIdAndDeletedFalse(organization.getId());
        dto.setPositionCount(positionCount);

        return dto;
    }

    /**
     * 检查是否会形成循环引用
     */
    private boolean isCircularReference(Long organizationId, Long newParentId) {
        if (organizationId.equals(newParentId)) {
            return true;
        }

        Organization parent = organizationRepository.findById(newParentId).orElse(null);
        while (parent != null && parent.getParentId() != null) {
            if (organizationId.equals(parent.getParentId())) {
                return true;
            }
            parent = organizationRepository.findById(parent.getParentId()).orElse(null);
        }

        return false;
    }

    /**
     * 更新子组织的层级和路径
     */
    private void updateChildrenLevelAndPath(Organization parent) {
        List<Organization> children = organizationRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(parent.getId());

        for (Organization child : children) {
            child.setOrgLevel(parent.getOrgLevel() + 1);

            String parentPath = StringUtils.hasText(parent.getParentPath())
                ? parent.getParentPath() + "/" + parent.getId()
                : "/" + parent.getId();
            child.setParentPath(parentPath);

            organizationRepository.save(child);

            // 递归更新子组织
            updateChildrenLevelAndPath(child);
        }
    }
}
