package com.hky.hr.organization.entity;

import com.hky.hr.organization.enums.OrganizationType;
import com.hky.hr.organization.enums.Status;
import com.hzwangda.edu.common.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 组织机构实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "org_organization", indexes = {
    @Index(name = "idx_org_code", columnList = "orgCode", unique = true),
    @Index(name = "idx_parent_id", columnList = "parentId"),
    @Index(name = "idx_org_type", columnList = "orgType"),
    @Index(name = "idx_org_status", columnList = "status")
})
public class Organization extends BaseEntity {

    /**
     * 组织编码
     */
    @NotBlank(message = "组织编码不能为空")
    @Size(max = 64, message = "组织编码长度不能超过64个字符")
    @Column(name = "org_code", length = 64, nullable = false, unique = true)
    private String orgCode;

    /**
     * 组织名称
     */
    @NotBlank(message = "组织名称不能为空")
    @Size(max = 128, message = "组织名称长度不能超过128个字符")
    @Column(name = "org_name", length = 128, nullable = false)
    private String orgName;

    /**
     * 组织简称
     */
    @Size(max = 128, message = "组织简称长度不能超过128个字符")
    @Column(name = "org_short_name", length = 128)
    private String orgShortName;

    /**
     * 组织类型
     */
    @NotNull(message = "组织类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "org_type", length = 32, nullable = false)
    private OrganizationType orgType;

    /**
     * 父级组织ID
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 组织层级
     */
    @Column(name = "org_level", nullable = false)
    private Integer orgLevel = 1;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 描述信息
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 负责人
     */
    @Size(max = 128, message = "负责人姓名长度不能超过128个字符")
    @Column(name = "leader", length = 128)
    private String leader;

    /**
     * 联系电话
     */
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 邮箱地址
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 128, message = "邮箱长度不能超过128个字符")
    @Column(name = "email", length = 128)
    private String email;

    /**
     * 地址
     */
    @Size(max = 256, message = "地址长度不能超过256个字符")
    @Column(name = "address", length = 256)
    private String address;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private Status status = Status.ACTIVE;

    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    private LocalDate effectiveDate;

    /**
     * 撤销日期
     */
    @Column(name = "withdraw_date")
    private LocalDate withdrawDate;

    /**
     * 批文号
     */
    @Size(max = 128, message = "批文号长度不能超过128个字符")
    @Column(name = "approval_doc_number", length = 128)
    private String approvalDocNumber;

    /**
     * 批准日期
     */
    @Column(name = "approval_date")
    private LocalDate approvalDate;

    /**
     * 设立日期
     */
    @Column(name = "establishment_date")
    private LocalDate establishmentDate;

    /**
     * 是否虚拟部门
     */
    @Column(name = "is_virtual")
    private Boolean isVirtual = false;

    /**
     * 是否临时机构
     */
    @Column(name = "is_temporary")
    private Boolean isTemporary = false;

    /**
     * 父级路径
     */
    @Size(max = 1000, message = "父级路径长度不能超过1000个字符")
    @Column(name = "parent_path", length = 1000)
    private String parentPath;

    /**
     * 备注信息
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    /**
     * 子组织列表
     */
    @OneToMany(mappedBy = "parentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Organization> children = new ArrayList<>();

    /**
     * 岗位列表
     */
    @OneToMany(mappedBy = "organizationId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Position> positions = new ArrayList<>();
}
