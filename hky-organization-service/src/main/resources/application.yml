server:
  port: 8082
  servlet:
    context-path: /organization-service
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: hky-organization-service

  profiles:
    active: dev

  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}

  # 数据源配置
  datasource:
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-Organization
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # Flyway数据库迁移配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: false  # 临时禁用断路器进行测试
    alphanumeric-ids:
      enabled: true

# Resilience4j断路器配置
resilience4j:
  circuitbreaker:
    instances:
      default:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
      audit-service:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 15s
        sliding-window-size: 8
      workflow-service:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 15s
        sliding-window-size: 8
      notification-service:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 15s
        sliding-window-size: 8
      dictionary-service:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 15s
        sliding-window-size: 8
  retry:
    instances:
      default:
        max-attempts: 3
        wait-duration: 500ms
        exponential-backoff-multiplier: 2
  timelimiter:
    instances:
      default:
        timeout-duration: 10s

# 组织管理服务配置
  organization:
    # 缓存配置
    cache:
      expire-hours: 24
      max-size: 1000

    # 变更审批配置
    change-approval:
      enabled: true
      default-timeout-hours: 72
      auto-approve-minor-changes: false

    # 编制管理配置
    staffing:
      auto-calculate-statistics: true
      budget-integration-enabled: false

    # 异步处理配置
    async:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 200
      keep-alive-seconds: 60

# 日志配置
logging:
  level:
    com.hky.hr.organization: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    # Feign 调试日志
    com.hzwangda.edu.common.client: DEBUG
    feign: DEBUG
    # 断路器日志
    io.github.resilience4j: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/organization-service.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Seata分布式事务配置
seata:
  enabled: false
  application-id: hky-organization-service
  tx-service-group: hky-hr-group
  service:
    vgroup-mapping:
      hky-hr-group: default
    grouplist:
      default: localhost:8091
  config:
    type: file
  registry:
    type: file

# API文档配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 杭科院人事管理系统 - 组织管理服务
    description: 提供组织架构管理、部门管理、岗位管理等功能
    version: 1.0.0
    contact:
      name: HKY-HR-System
      email: <EMAIL>

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

# 开发环境禁用 Seata
seata:
  enabled: false

logging:
  level:
    root: INFO
    com.hky.hr.organization: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: ********************************************************************************************************************************
  jpa:
    hibernate:
      ddl-auto: create-drop

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    root: WARN
    com.hky.hr.organization: INFO
